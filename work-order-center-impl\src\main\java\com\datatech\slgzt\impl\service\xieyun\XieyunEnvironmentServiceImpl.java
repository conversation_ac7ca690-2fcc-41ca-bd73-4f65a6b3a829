package com.datatech.slgzt.impl.service.xieyun;

import com.datatech.slgzt.config.XieyunProperties;
import com.datatech.slgzt.model.nostander.CQModel;
import com.datatech.slgzt.service.xieyun.XieyunEnvironmentService;
import com.datatech.slgzt.utils.ObjNullUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.explore.JobExplorer;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.JobOperator;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @description 协云环境服务实现
 * @date 2025年 04月10日 11:37:06
 */
@Slf4j
@Service
public class XieyunEnvironmentServiceImpl implements XieyunEnvironmentService {

    @Resource
    private XieyunProperties xieyunProperties;

    @Resource
    private JobLauncher jobLauncher;

    @Resource
    private JobRegistry jobRegistry;

    @Resource
    private JobOperator jobOperator;

    //job历史执行器
    @Resource
    private JobExplorer jobExplorer;


    /**
     * 创建基础环境
     *
     * @param subOrderId 子订单ID
     * @param executionId 执行ID
     * @param cqModel 容器配额模型
     */
    @SneakyThrows
    @Override
    @Async("xieyunTaskExecutor")
    public void createBaseEnvironment(Long subOrderId, Long executionId, CQModel cqModel) {
        //
        if (ObjNullUtils.isNotNull(executionId)) {
            // 如果执行ID不为空，说明是重试
            JobExecution jobExecution = jobExplorer.getJobExecution(executionId);
            if (jobExecution != null) {
                // 如果存在，则直接返回
                BatchStatus status = jobExecution.getStatus();
                if (status == BatchStatus.COMPLETED) {
                    // 如果状态是完成，则直接返回
                    return;
                } else if (status == BatchStatus.FAILED) {
                    // 如果状态是失败，则重新启动
                    jobOperator.restart(executionId);
                    return;
                } else if (status == BatchStatus.STOPPED) {
                    // 如果状态是停止，则重新启动
                    jobOperator.restart(executionId);
                }
                // 其他状态直接返回
                return;
            }
        }
        log.info("从工单获取到容器配额参数，subOrderId: {}, cqModel: {}", subOrderId, cqModel);
        JobParameters jobParameters = new JobParametersBuilder()
                .addLong("subOrderId", subOrderId)
                .addString("createUserName", cqModel.getA4Account())
                .addString("name", cqModel.getA4Account())
                .addString("mobile", cqModel.getA4Phone())
                .addString("email", cqModel.getA4Account() + "@zj.chinamobile.com")
                .addString("orgName", cqModel.getBusinessSystemName())
                .addString("orgDesc", String.valueOf(cqModel.getBusinessSystemId()))
                .addString("projectName", cqModel.getCqName())
                .addString("projectDesc", cqModel.getCqName())
                .addString("clusterName", xieyunProperties.getClusterNameByRegionCode(cqModel.getRegionCode()))
                .addString("nodePoolName", cqModel.getRegionCode())
                .addString("cpuValue", String.valueOf(cqModel.getVCpus()))
                .addString("memoryValue", String.valueOf(cqModel.getRam()))
                .addString("gpuCore", String.valueOf(cqModel.getGpuCore()))
                .addString("gpuRatio", String.valueOf(cqModel.getGpuRatio()))
                .addString("gpuVirtualCore", String.valueOf(cqModel.getGpuVirtualCore()))
                .addString("gpuVirtualMemory", String.valueOf(cqModel.getGpuVirtualMemory()))
                .addLong("time", System.currentTimeMillis())
                .toJobParameters();
        jobLauncher.run(jobRegistry.getJob("xieyun_env_createJob"), jobParameters);
    }

}
