package com.datatech.slgzt.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务配置类
 * 
 * <AUTHOR>
 * @description 配置异步任务执行器
 * @date 2025年 04月10日 15:30:00
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * 异步任务执行器
     * 用于处理@Async注解的方法
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        log.info("开始初始化异步任务线程池");
        
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：CPU核心数
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(corePoolSize);
        log.info("异步任务线程池核心线程数: {}", corePoolSize);
        
        // 最大线程数：CPU核心数 * 2
        int maxPoolSize = corePoolSize * 2;
        executor.setMaxPoolSize(maxPoolSize);
        log.info("异步任务线程池最大线程数: {}", maxPoolSize);
        
        // 队列容量：CPU核心数 * 10
        int queueCapacity = corePoolSize * 10;
        executor.setQueueCapacity(queueCapacity);
        log.info("异步任务线程池队列容量: {}", queueCapacity);
        
        // 线程名称前缀
        executor.setThreadNamePrefix("async-woc-");
        
        // 线程空闲时间：60秒
        executor.setKeepAliveSeconds(60);
        
        // 当线程池和队列都满时的拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间：30秒
        executor.setAwaitTerminationSeconds(30);
        
        // 初始化线程池
        executor.initialize();
        
        log.info("异步任务线程池初始化完成");
        return executor;
    }
    
    /**
     * 协云环境创建专用执行器
     * 用于处理协云环境创建的异步任务
     */
    @Bean("xieyunTaskExecutor")
    public Executor xieyunTaskExecutor() {
        log.info("开始初始化协云环境创建异步任务线程池");
        
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：2个（协云环境创建任务相对较少）
        executor.setCorePoolSize(2);
        
        // 最大线程数：5个
        executor.setMaxPoolSize(5);
        
        // 队列容量：20个
        executor.setQueueCapacity(20);
        
        // 线程名称前缀
        executor.setThreadNamePrefix("xieyun-async-");
        
        // 线程空闲时间：300秒（5分钟）
        executor.setKeepAliveSeconds(300);
        
        // 当线程池和队列都满时的拒绝策略：抛出异常
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间：60秒
        executor.setAwaitTerminationSeconds(60);
        
        // 初始化线程池
        executor.initialize();
        
        log.info("协云环境创建异步任务线程池初始化完成");
        return executor;
    }
}
