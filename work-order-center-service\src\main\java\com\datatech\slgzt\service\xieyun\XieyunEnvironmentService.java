package com.datatech.slgzt.service.xieyun;


import com.datatech.slgzt.model.nostander.CQModel;

import java.util.concurrent.CompletableFuture;

public interface XieyunEnvironmentService {

    /**
     * 创建基础环境（异步）
     *
     * @param subOrderId 子订单ID
     * @param executionId 执行ID
     * @param cqModel 容器配额模型
     * @return 异步任务执行ID
     */
    CompletableFuture<String> createBaseEnvironment(Long subOrderId, Long executionId, CQModel cqModel);

}
