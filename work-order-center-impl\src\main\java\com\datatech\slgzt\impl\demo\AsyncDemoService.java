package com.datatech.slgzt.impl.demo;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 异步功能演示服务
 * 
 * <AUTHOR>
 * @description 演示如何正确使用@Async注解
 * @date 2025年 04月10日 16:00:00
 */
@Slf4j
@Service
public class AsyncDemoService {

    /**
     * 同步方法（用于对比）
     */
    public String syncMethod(String input) {
        String currentThread = Thread.currentThread().getName();
        log.info("同步方法开始执行，当前线程: {}, 输入参数: {}", currentThread, input);
        
        try {
            // 模拟耗时操作
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        String result = "同步处理结果: " + input;
        log.info("同步方法执行完成，当前线程: {}, 返回结果: {}", currentThread, result);
        return result;
    }

    /**
     * 异步方法（返回CompletableFuture）
     */
    @Async("taskExecutor")
    public CompletableFuture<String> asyncMethod(String input) {
        String currentThread = Thread.currentThread().getName();
        log.info("异步方法开始执行，当前线程: {}, 输入参数: {}", currentThread, input);
        
        try {
            // 模拟耗时操作
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        String result = "异步处理结果: " + input;
        log.info("异步方法执行完成，当前线程: {}, 返回结果: {}", currentThread, result);
        return CompletableFuture.completedFuture(result);
    }

    /**
     * 异步方法（无返回值）
     */
    @Async("taskExecutor")
    public void asyncVoidMethod(String input) {
        String currentThread = Thread.currentThread().getName();
        log.info("异步无返回值方法开始执行，当前线程: {}, 输入参数: {}", currentThread, input);
        
        try {
            // 模拟耗时操作
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("异步无返回值方法执行完成，当前线程: {}", currentThread);
    }

    /**
     * 演示正确的异步调用方式
     */
    public void demonstrateAsyncUsage() {
        log.info("=== 异步功能演示开始 ===");
        
        // 1. 同步调用（会阻塞）
        log.info("1. 同步调用演示：");
        long startTime = System.currentTimeMillis();
        String syncResult = syncMethod("测试数据");
        long syncTime = System.currentTimeMillis() - startTime;
        log.info("同步调用耗时: {}ms, 结果: {}", syncTime, syncResult);
        
        // 2. 异步调用（不会阻塞）
        log.info("2. 异步调用演示：");
        startTime = System.currentTimeMillis();
        CompletableFuture<String> future = asyncMethod("测试数据");
        long asyncCallTime = System.currentTimeMillis() - startTime;
        log.info("异步调用提交耗时: {}ms（注意：这里不包含实际执行时间）", asyncCallTime);
        
        // 3. 处理异步结果
        future.thenAccept(result -> {
            log.info("异步结果回调: {}", result);
        }).exceptionally(throwable -> {
            log.error("异步执行失败", throwable);
            return null;
        });
        
        // 4. 异步无返回值调用
        log.info("3. 异步无返回值调用演示：");
        asyncVoidMethod("无返回值测试");
        
        log.info("=== 异步功能演示结束 ===");
    }
}
