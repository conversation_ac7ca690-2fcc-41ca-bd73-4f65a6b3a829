package com.datatech.slgzt.impl.service.xieyun;

// import com.datatech.slgzt.config.AsyncConfig;
import com.datatech.slgzt.config.XieyunProperties;
import com.datatech.slgzt.model.nostander.CQModel;
import com.datatech.slgzt.service.xieyun.XieyunEnvironmentService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.explore.JobExplorer;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.JobOperator;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 协云环境服务异步测试
 *
 * <AUTHOR>
 * @description 测试异步执行功能
 * @date 2025年 04月10日 15:45:00
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
// @SpringJUnitConfig(classes = {AsyncConfig.class})
public class XieyunEnvironmentServiceAsyncTest {

    @Mock
    private XieyunProperties xieyunProperties;

    @Mock
    private JobLauncher jobLauncher;

    @Mock
    private JobRegistry jobRegistry;

    @Mock
    private JobOperator jobOperator;

    @Mock
    private JobExplorer jobExplorer;

    @Mock
    private Job job;

    @Mock
    private JobExecution jobExecution;

    private XieyunEnvironmentServiceImpl xieyunEnvironmentService;

    @BeforeEach
    void setUp() {
        xieyunEnvironmentService = new XieyunEnvironmentServiceImpl();
        // 使用反射设置私有字段
        setField(xieyunEnvironmentService, "xieyunProperties", xieyunProperties);
        setField(xieyunEnvironmentService, "jobLauncher", jobLauncher);
        setField(xieyunEnvironmentService, "jobRegistry", jobRegistry);
        setField(xieyunEnvironmentService, "jobOperator", jobOperator);
        setField(xieyunEnvironmentService, "jobExplorer", jobExplorer);
    }

    @Test
    void testAsyncExecution() throws Exception {
        // 准备测试数据
        Long subOrderId = 12345L;
        Long executionId = null;
        CQModel cqModel = createTestCQModel();

        // Mock依赖
        when(xieyunProperties.getClusterNameByRegionCode(anyString())).thenReturn("test-cluster");
        when(jobRegistry.getJob("xieyun_env_createJob")).thenReturn(job);
        when(jobExecution.getId()).thenReturn(67890L);
        when(jobLauncher.run(any(Job.class), any())).thenReturn(jobExecution);

        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String currentThread = Thread.currentThread().getName();
        log.info("测试开始，当前线程: {}", currentThread);

        // 异步调用
        CompletableFuture<String> future = xieyunEnvironmentService.createBaseEnvironment(subOrderId, executionId, cqModel);

        // 等待异步执行完成
        String result = future.get(10, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();

        // 验证结果
        assertNotNull(result);
        assertEquals("67890", result);

        log.info("测试完成，总耗时: {}ms", endTime - startTime);
        log.info("返回结果: {}", result);
    }

    /**
     * 创建测试用的CQModel
     */
    private CQModel createTestCQModel() {
        CQModel cqModel = new CQModel();
        cqModel.setCqName("test-cq");
        cqModel.setVCpus(4);
        cqModel.setRam(8);
        cqModel.setGpuCore(1);
        cqModel.setGpuRatio(1);
        cqModel.setGpuVirtualCore(2);
        cqModel.setGpuVirtualMemory(4);
        cqModel.setA4Account("testuser");
        cqModel.setA4Phone("***********");
        cqModel.setBusinessSystemName("测试业务系统");
        cqModel.setBusinessSystemId(1001L);
        cqModel.setRegionCode("test-region");
        return cqModel;
    }

    /**
     * 使用反射设置私有字段
     */
    private void setField(Object target, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(target, value);
        } catch (Exception e) {
            log.error("设置字段失败: {}", fieldName, e);
        }
    }
}
